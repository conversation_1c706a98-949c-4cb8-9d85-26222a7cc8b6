{"name": "isiscb-tagger", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.9", "element-plus": "^2.9.10", "fast-xml-parser": "^5.2.5", "papaparse": "^5.4.1", "vue": "^3.5.13"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "vite": "^6.0.5", "vite-plugin-vue-devtools": "^7.6.8"}}