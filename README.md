### **User Guide: Bibliographic Items Tagging Tool**

Welcome to the AI-driven tool for tagging bibliographic items! This guide will walk you through the process of importing your data, generating tags, reviewing them, and exporting the final results.

#### **Quick Overview**

The workflow is broken down into three main steps, which are laid out in the application:

1.  **Import Items:** Add your bibliographic data from Zotero, a CSV file, or by manual entry.
2.  **Review and Edit Tags:** Use the AI to generate tags, then review and refine them.
3.  **Export Result:** Download your newly tagged data in various formats or save it back to Zotero.

You can access **Advanced Settings** at any time using the floating settings button (⚙️) on the right side of the screen.

---

### **Part 1: Import Bibliographic Items**

First, you need to import the items you want to tag. You have three options, presented in a collapsible menu.

#### **Method A: Import from Zotero (Recommended)**

This is the most powerful option, as it allows you to save tags back to your Zotero library later.

1.  **Library ID:** Enter your Zotero User ID (for a personal library) or Group ID (for a shared group library).
    * You can find your **User ID** in your Zotero security settings.
    * The **Group ID** is in the URL of your group's library page on the Zotero website.
2.  **API Key:** Create and enter a Zotero API key from your Zotero security settings. This is required for the tool to access your library.
3.  **Tag:** Enter a tag that you have applied in Zotero to the items you wish to import (e.g., `to-be-tagged`). The tool will fetch all items with this specific tag.
4.  Click **Fetch Zotero Items**. The tool will connect to your library and import the relevant items, showing a preview when complete.

#### **Method B: Upload CSV**

1.  Click the **Upload** button under the "Upload CSV" section.
2.  Select a CSV file from your computer.
3.  **CSV exported from Zotero:**
    * Keep this box **checked** (default) if your CSV file was exported from Zotero and has a standard header row. The tool will automatically map fields like `Title`, `Abstract Note`, `Author`, etc.
    * **Uncheck** this box if your CSV is a simple two-column file without a header. The tool will treat the first column as the `title` and the second as the `abstract`.

#### **Method C: Manually Add Items**

For quick additions or testing, you can add items one by one.

1.  Enter the **Title** and **Abstract** in the provided text boxes.
2.  Click **Add to List**.
3.  Use the **Remove Last Item** and **Undo Remove** buttons to manage your manually added list.

#### **Previewing Your Items**

Once you've imported items, a preview card will appear showing the total count and the titles of the first and last few items in your list. The source of the data (e.g., "Zotero," "local file") will also be displayed.

---

### **Part 2: Generating and Reviewing Tags**

After importing your items, you can generate tags for them.

1.  **Tag Items:** Click the green **Tag Items** button.
2.  **Batch Size:** The tool processes items in batches to communicate with the AI. A suggested batch size is provided, but you can set your own number. Smaller batches provide more frequent progress updates; larger batches can be slightly faster overall.
3.  **Processing:** A loading message will appear showing the elapsed time. Batches are processed one by one, and you will receive a success message as each batch is completed.

#### **Reviewing and Editing**

Once processing is finished, the results will appear in the "Step 2. Review and Edit Matched Tags" section.

* **Expand/Collapse:** You can expand each item to see its details or use the **Expand All** / **Collapse All** button for convenience.
* **Matched Tags:** This is the primary list of tags for the item.
    * **Deselect a Tag:** Simply click on a tag to deselect it. It will become grayed out and struck through, marking it for exclusion from the final export. Click it again to re-activate it.
    * **Add a New Tag:**
        1.  Start typing in the input box ("*Type to search or Enter to add...*").
        2.  A dropdown will appear with suggestions from the main tag pool. You can click a suggestion to add it.
        3.  To add a completely new custom tag, type your tag and press **Enter**.
    * **Remove an Added Tag:** Click the 'x' on any newly added tag to remove it.
* **Reference Tags (Concept, Person/Org, Time/Place):** These are additional keywords extracted by the AI for your reference. To add one to your main "Matched Tags" list, you can either **double-click** it or **drag-and-drop** it into the "Matched Tags" area.

---

### **Part 3: Exporting Your Results**

After reviewing and editing the tags, you can export your work.

#### **Saving to Zotero**

* If you imported your data from Zotero, the **Save Matched Tags to Zotero** button will be enabled.
* Clicking this will update the original items in your Zotero library, adding the new tags. It intelligently merges the new tags with any existing tags the item already had.

#### **Downloading Files**

1.  **Select Format:** Choose your desired file format: **RIS** (recommended for Zotero), **BibTeX**, or **CSV**.
2.  **Tag Formatting Options (Optional):** Before downloading, you can expand this section to customize how your tags are formatted.
    * **Add prefix/suffix to customized tags:** Adds a special mark (e.g., `[NEW]`) to tags that you created, which were not part of the original tag pool.
    * **Add metadata suffix to matched tags:** Appends metadata to tags, such as the tag's unique ID from the tag pool (e.g., `my-tag [record_id]`).
    * **Add custom suffix:** Appends a custom string (e.g., `[IsisCBtag]`) to all matched tags.
    * **Add an extra tag for all processed items:** Adds a single, consistent tag (e.g., `processed-by-tagger`) to every item that was processed.
3.  **Options:** For each format, you can click the **Options** button to customize the export. This allows you to select which data fields (e.g., Title, Author, Volume) to include in the exported file.
4.  **Download:** Click the **Download As** button to save the file to your computer.

---

### **Advanced Settings**

Click the floating gear icon (⚙️) to open the Advanced Settings drawer. Here you can:

* **Select Tag Generator:** Choose the AI model used for generating tags. `gpt-4o-mini` is the default, offering a balance of speed and quality. `pytextrank` is faster but less accurate.
* **Customize API's URL:** If you are hosting the backend service yourself or using a different endpoint, you can change the API URLs here.