<template>
  <div>
    <!-- Data Import Section -->
    <el-divider class="el-divider--nowrap">Step 1. Import Bibliographic Items</el-divider>
    <el-row justify="center">
      <el-col :span="12" :xs="24">
        <!-- Zotero Import Section -->
        <el-collapse v-model="activeDataSource">
          <el-collapse-item name="zotero">
            <template #title>
              <div class="source-title truncate-title">
                <el-icon>
                  <Download />
                </el-icon>
                Import from Zotero (Recommended)
              </div>
            </template>

            <el-form :model="zoteroConfig" label-position="top" class="zotero-form">
              <el-form-item label="Library ID" :rules="[{ required: true, message: 'Library ID is required' }]">
                <el-input v-model="zoteroConfig.libraryId" placeholder="Enter Zotero library or user ID">
                  <template #prefix>
                    <el-tooltip>
                      <template #content>
                        For personal library, use your <a
                          href="https://www.zotero.org/settings/security#applications" target="_blank"
                          style="color: #409EFF; text-decoration: underline;">User ID</a>. For group library, use
                        <a href="https://www.zotero.org/groups/" target="_blank"
                          style="color: #409EFF; text-decoration: underline;">Group ID</a> in the URL of the group
                        library.
                      </template>
                      <el-icon>
                        <InfoFilled />
                      </el-icon>
                    </el-tooltip>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="API Key" :rules="[{ required: true, message: 'API key is required' }]">
                <el-input v-model="zoteroConfig.apiKey" type="password" placeholder="Enter Zotero API key"
                  show-password>
                  <template #prefix>
                    <el-tooltip>
                      <template #content>
                        Create a new key in <a href="https://www.zotero.org/settings/security#applications"
                          target="_blank">Zotero settings</a>
                        if you do not have one.
                      </template>
                      <el-icon>
                        <Key />
                      </el-icon>
                    </el-tooltip>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="Tag" :rules="[{ required: true, message: 'Tag is required' }]">
                <el-select v-model="zoteroConfig.tag" placeholder="Items with this tag will be imported"
                  filterable allow-create clearable :default-first-option="true">
                  <template #prefix>
                    <el-icon>
                      <PriceTag />
                    </el-icon>
                  </template>
                  <!-- Predefined frequently used tags -->
                  <el-option v-for="(tag, index) in frequentlyUsedTagsForImport" :key="index" :label="tag"
                    :value="tag" />
                </el-select>
              </el-form-item>
              <el-row justify="center">
                <el-button type="primary" @click="handleFetchZoteroItems" :loading="isZoteroLoading"
                  :disabled="!isZoteroConfigValid">
                  {{ isZoteroLoading ? 'Fetching Items...' : 'Fetch Zotero Items' }}
                </el-button>
              </el-row>
            </el-form>
            <template v-if="isZoteroLoading && fetchProgress > 0">
              <el-progress :percentage="fetchProgress" :format="format => `${format}% Complete`"
                status="success" />
            </template>
          </el-collapse-item>

          <el-collapse-item name="csv-upload" class="center-content">
            <template #title>
              <div class="source-title truncate-title">
                <el-icon>
                  <Upload />
                </el-icon>
                Upload CSV
              </div>
            </template>

            <el-upload class="center-content" :before-upload="handleCSVUpload" accept=".csv"
              :show-file-list="false">
              <el-button type="primary">
                Upload
              </el-button>
            </el-upload>
            <el-checkbox v-model="csvHasZoteroHeader">CSV exported from Zotero <el-tooltip effect="dark"
                placement="right">
                <template #content> Uncheck if the CSV file does not have a header. <br /> The first two columns
                  will be used as title and abstract.</template>
                <el-icon>
                  <InfoFilled />
                </el-icon>
              </el-tooltip></el-checkbox>

          </el-collapse-item>
          <el-collapse-item name="manual-input">
            <template #title>
              <div class="source-title truncate-title">
                <el-icon>
                  <EditPen />
                </el-icon>
                Manually add items
              </div>
            </template>
            <el-form label-position="top" class="article-form" @submit.prevent="handleFormSubmit">
              <div class="article-input">

                <el-form-item label="Title" class="form-item">
                  <el-input v-model="currentInput.title" placeholder="Enter the title" @keyup.enter="handleAppendItem"
                    class="title-input" />
                </el-form-item>

                <el-form-item label="Abstract" class="form-item">
                  <el-input v-model="currentInput.abstract" type="textarea" placeholder="Enter the abstract"
                    :rows="4" resize="vertical" class="abstract-input" />
                </el-form-item>
              </div>
            </el-form>

            <!-- Action buttons -->
            <el-row justify="center" class="center-content">
              <el-button type="primary" :icon="Plus" @click="handleAppendItem"
                :disabled="!currentInput.title && !currentInput.abstract">
                Add to List
              </el-button>
              <el-button type="danger" :icon="Minus" @click="handleRemoveLastItem" :disabled="biblioItems.length === 0">
                Remove Last Item
              </el-button>
              <el-button type="warning" :icon="RefreshLeft" @click="handleUndoRemove"
                :disabled="removedItems.length === 0">
                Undo Remove ({{ removedItems.length }})
              </el-button>
            </el-row>
          </el-collapse-item>
        </el-collapse>

        <!-- Preview Section -->
        <div v-if="biblioItems.length" class="csv-preview">
          <el-card>
            <template #header>
              <div class="preview-header">
                <el-text size="large">Preview of the {{ biblioItems.length }} items to be tagged:</el-text>
                <el-tag type="info" v-if="currentSource">
                  Source: {{ currentSource }}
                </el-tag>
              </div>
            </template>

            <!-- First 3 items -->
            <template v-if="biblioItems.length > 0">
              <div class="preview-section">
                <div v-for="(item, index) in biblioItems.slice(0, 3)" :key="`first-${index}`"
                  class="text item truncate-title">
                  {{ index + 1 }}. {{ item.title }}
                </div>
              </div>
            </template>

            <!-- Separator for middle items -->
            <div v-if="biblioItems.length > 6" class="preview-separator">
              <el-divider>
                <el-text class="more-items">{{ biblioItems.length - 6 }} more items</el-text>
              </el-divider>
            </div>

            <!-- Last 3 items -->
            <template v-if="biblioItems.length > 3 && biblioItems.length <= 6">
              <div class="preview-section">
                <div v-for="(item, index) in biblioItems.slice(3)" :key="`last-${index}`"
                  class="text item truncate-title">
                  {{ index + 4 }}. {{ item.title }}
                </div>
              </div>
            </template>

            <template v-if="biblioItems.length > 6">
              <div class="preview-section">
                <div v-for="(item, index) in biblioItems.slice(-3)" :key="`last-${index}`"
                  class="text item truncate-title">
                  {{ biblioItems.length - 2 + index }}. {{ item.title }}
                </div>
              </div>
            </template>
          </el-card>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import axios from 'axios'
import Papa from 'papaparse'
import {
  ElDivider,
  ElRow,
  ElCol,
  ElCollapse,
  ElCollapseItem,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElSelect,
  ElOption,
  ElTooltip,
  ElIcon,
  ElProgress,
  ElUpload,
  ElCheckbox,
  ElCard,
  ElText,
  ElTag,
  ElMessage
} from 'element-plus'
import { Download, Upload, EditPen, Plus, Minus, RefreshLeft, InfoFilled, Key, PriceTag } from '@element-plus/icons-vue'

// Zotero-related state
const zoteroConfig = ref({
  libraryId: '',
  apiKey: '',
  tag: ''
})
const zoteroBaseUrl = ref('')
const MAX_ITEMS_PER_REQUEST = 100
const isZoteroLoading = ref(false)
const frequentlyUsedTagsForImport = ref([
  'to-be-tagged',
  'IsisCB project',
])
const fetchProgress = ref(0)

// CSV-related state
const csvHasZoteroHeader = ref(true)

// Manual item management state
const currentInput = ref({ title: '', abstract: '' })
const removedItems = ref([])

// Data source management state
const activeDataSource = ref(['csv']) // For collapse control
const currentSource = ref('') // To track data source

// Bibliographic items state
const biblioItems = ref([]) // This is for storing the original items imported from data sources

// Computed property for form validation
const isZoteroConfigValid = computed(() => {
  const config = zoteroConfig.value
  if (!config) return false
  const { libraryId, apiKey, tag } = config
  return !!(libraryId && apiKey && tag)
})

// Props
const props = defineProps({
  clearTrigger: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits([
  'itemsUpdated' // New emit for when biblioItems change
])

// Watch for clear trigger from parent
watch(() => props.clearTrigger, (newValue, oldValue) => {
  if (newValue > oldValue) {
    // Clear internal state
    biblioItems.value = []
    removedItems.value = []
    currentSource.value = ''
    currentInput.value = { title: '', abstract: '' }
  }
})

// Event handlers
const handleFetchZoteroItems = () => {
  fetchZoteroItems()
}

const handleCSVUpload = (file) => {
  Papa.parse(file, {
    complete: (result) => {
      let data;
      if (csvHasZoteroHeader.value) {
        // If the CSV has a header, use named fields
        data = result.data
          .filter(row => row.Key) // Ensure the row has a Key (or any required field)
          .map((row) => ({
            key: row.Key?.replace(/^"|"$/g, '').trim() || '',
            itemType: row['Item Type']?.replace(/^"|"$/g, '').trim() || '',
            publicationYear: row['Publication Year']?.replace(/^"|"$/g, '').trim() || '',
            author: row.Author?.replace(/^"|"$/g, '').trim() || '',
            title: row.Title?.replace(/^"|"$/g, '').trim() || '',
            publicationTitle: row['Publication Title']?.replace(/^"|"$/g, '').trim() || '',
            isbn: row.ISBN?.replace(/^"|"$/g, '').trim() || '',
            issn: row.ISSN?.replace(/^"|"$/g, '').trim() || '',
            doi: row.DOI?.replace(/^"|"$/g, '').trim() || '',
            url: row.Url?.replace(/^"|"$/g, '').trim() || '',
            abstract: row['Abstract Note']?.replace(/^"|"$/g, '').trim() || '',
            date: row.Date?.replace(/^"|"$/g, '').trim() || '',
            dateAdded: row['Date Added']?.replace(/^"|"$/g, '').trim() || '',
            dateModified: row['Date Modified']?.replace(/^"|"$/g, '').trim() || '',
            accessDate: row['Access Date']?.replace(/^"|"$/g, '').trim() || '',
            pages: row.Pages?.replace(/^"|"$/g, '').trim() || '',
            numPages: row['Num Pages']?.replace(/^"|"$/g, '').trim() || '',
            issue: row.Issue?.replace(/^"|"$/g, '').trim() || '',
            volume: row.Volume?.replace(/^"|"$/g, '').trim() || '',
            numberOfVolumes: row['Number Of Volumes']?.replace(/^"|"$/g, '').trim() || '',
            journalAbbreviation: row['Journal Abbreviation']?.replace(/^"|"$/g, '').trim() || '',
            shortTitle: row['Short Title']?.replace(/^"|"$/g, '').trim() || '',
            series: row.Series?.replace(/^"|"$/g, '').trim() || '',
            seriesNumber: row['Series Number']?.replace(/^"|"$/g, '').trim() || '',
            seriesText: row['Series Text']?.replace(/^"|"$/g, '').trim() || '',
            seriesTitle: row['Series Title']?.replace(/^"|"$/g, '').trim() || '',
            publisher: row.Publisher?.replace(/^"|"$/g, '').trim() || '',
            place: row.Place?.replace(/^"|"$/g, '').trim() || '',
            language: row.Language?.replace(/^"|"$/g, '').trim() || '',
            rights: row.Rights?.replace(/^"|"$/g, '').trim() || '',
            type: row.Type?.replace(/^"|"$/g, '').trim() || '',
            archive: row.Archive?.replace(/^"|"$/g, '').trim() || '',
            archiveLocation: row['Archive Location']?.replace(/^"|"$/g, '').trim() || '',
            libraryCatalog: row['Library Catalog']?.replace(/^"|"$/g, '').trim() || '',
            callNumber: row['Call Number']?.replace(/^"|"$/g, '').trim() || '',
            extra: row.Extra?.replace(/^"|"$/g, '').trim() || '',
            notes: row.Notes?.replace(/^"|"$/g, '').trim() || '',
            fileAttachments: row['File Attachments']?.replace(/^"|"$/g, '').trim() || '',
            linkAttachments: row['Link Attachments']?.replace(/^"|"$/g, '').trim() || '',
            editor: row.Editor?.replace(/^"|"$/g, '').trim() || '',
            seriesEditor: row['Series Editor']?.replace(/^"|"$/g, '').trim() || '',
            translator: row.Translator?.replace(/^"|"$/g, '').trim() || '',
            contributor: row.Contributor?.replace(/^"|"$/g, '').trim() || '',
            attorneyAgent: row['Attorney Agent']?.replace(/^"|"$/g, '').trim() || '',
            bookAuthor: row['Book Author']?.replace(/^"|"$/g, '').trim() || '',
            castMember: row['Cast Member']?.replace(/^"|"$/g, '').trim() || '',
            commenter: row.Commenter?.replace(/^"|"$/g, '').trim() || '',
            composer: row.Composer?.replace(/^"|"$/g, '').trim() || '',
            cosponsor: row.Cosponsor?.replace(/^"|"$/g, '').trim() || '',
            counsel: row.Counsel?.replace(/^"|"$/g, '').trim() || '',
            interviewer: row.Interviewer?.replace(/^"|"$/g, '').trim() || '',
            producer: row.Producer?.replace(/^"|"$/g, '').trim() || '',
            recipient: row.Recipient?.replace(/^"|"$/g, '').trim() || '',
            reviewedAuthor: row['Reviewed Author']?.replace(/^"|"$/g, '').trim() || '',
            scriptwriter: row.Scriptwriter?.replace(/^"|"$/g, '').trim() || '',
            wordsBy: row['Words By']?.replace(/^"|"$/g, '').trim() || '',
            guest: row.Guest?.replace(/^"|"$/g, '').trim() || '',
            number: row.Number?.replace(/^"|"$/g, '').trim() || '',
            edition: row.Edition?.replace(/^"|"$/g, '').trim() || '',
            runningTime: row['Running Time']?.replace(/^"|"$/g, '').trim() || '',
            scale: row.Scale?.replace(/^"|"$/g, '').trim() || '',
            medium: row.Medium?.replace(/^"|"$/g, '').trim() || '',
            artworkSize: row['Artwork Size']?.replace(/^"|"$/g, '').trim() || '',
            filingDate: row['Filing Date']?.replace(/^"|"$/g, '').trim() || '',
            applicationNumber: row['Application Number']?.replace(/^"|"$/g, '').trim() || '',
            assignee: row.Assignee?.replace(/^"|"$/g, '').trim() || '',
            issuingAuthority: row['Issuing Authority']?.replace(/^"|"$/g, '').trim() || '',
            country: row.Country?.replace(/^"|"$/g, '').trim() || '',
            meetingName: row['Meeting Name']?.replace(/^"|"$/g, '').trim() || '',
            conferenceName: row['Conference Name']?.replace(/^"|"$/g, '').trim() || '',
            court: row.Court?.replace(/^"|"$/g, '').trim() || '',
            references: row.References?.replace(/^"|"$/g, '').trim() || '',
            reporter: row.Reporter?.replace(/^"|"$/g, '').trim() || '',
            legalStatus: row['Legal Status']?.replace(/^"|"$/g, '').trim() || '',
            priorityNumbers: row['Priority Numbers']?.replace(/^"|"$/g, '').trim() || '',
            programmingLanguage: row['Programming Language']?.replace(/^"|"$/g, '').trim() || '',
            version: row.Version?.replace(/^"|"$/g, '').trim() || '',
            system: row.System?.replace(/^"|"$/g, '').trim() || '',
            code: row.Code?.replace(/^"|"$/g, '').trim() || '',
            codeNumber: row['Code Number']?.replace(/^"|"$/g, '').trim() || '',
            section: row.Section?.replace(/^"|"$/g, '').trim() || '',
            session: row.Session?.replace(/^"|"$/g, '').trim() || '',
            committee: row.Committee?.replace(/^"|"$/g, '').trim() || '',
            history: row.History?.replace(/^"|"$/g, '').trim() || '',
            legislativeBody: row['Legislative Body']?.replace(/^"|"$/g, '').trim() || ''
          }))
          .filter(item => item.title !== '' || item.abstractNote !== ''); // Filter out empty items
      } else {
        // If the CSV does not have a header, use positional indexing
        data = result.data
          .filter(row => row.length >= 2) // Ensure the row has at least two columns
          .map((row) => ({
            title: row[0]?.replace(/^"|"$/g, '').trim() || '',
            abstract: row[1]?.replace(/^"|"$/g, '').trim() || ''
          }))
          .filter(item => item.title !== '' || item.abstract !== ''); // Filter out empty items
      }

      // Update internal state
      biblioItems.value = data
      removedItems.value = [] // Clear removed items history when new CSV is uploaded
      currentSource.value = 'local file'
      // Emit the updated items to parent
      emit('itemsUpdated', biblioItems.value, removedItems.value, currentSource.value)
      ElMessage.success(`CSV file processed: ${data.length} articles found`)
    },
    header: csvHasZoteroHeader.value, // Use the first row as headers only if hasHeader is true
    skipEmptyLines: true
  })
  return false // Prevent default upload behavior
}

const handleAppendItem = () => {
  if (currentInput.value.title.trim() || currentInput.value.abstract.trim()) {
    const newItem = {
      title: currentInput.value.title.trim(),
      abstract: currentInput.value.abstract.trim()
    }
    // Add item to internal state
    biblioItems.value.push(newItem)
    // Clear input fields after adding
    currentInput.value = { title: '', abstract: '' }
    // Emit the updated items
    emit('itemsUpdated', biblioItems.value, removedItems.value, currentSource.value)
    ElMessage.success('Item added successfully!')
  }
}

const handleRemoveLastItem = () => {
  if (biblioItems.value.length > 0) {
    const removedItem = biblioItems.value.pop()
    removedItems.value.push(removedItem)
    // Emit the updated items
    emit('itemsUpdated', biblioItems.value, removedItems.value, currentSource.value)
    ElMessage.success('Last item removed. Click Undo to restore.')
  }
}

const handleUndoRemove = () => {
  if (removedItems.value.length > 0) {
    const itemToRestore = removedItems.value.pop()
    biblioItems.value.push(itemToRestore)
    // Emit the updated items
    emit('itemsUpdated', biblioItems.value, removedItems.value, currentSource.value)
    ElMessage.success('Item restored successfully!')
  }
}

const handleFormSubmit = (event) => {
  event.preventDefault() // Prevent default form submission
  handleAppendItem() // Call appendItem function
}

// Zotero fetch function
const fetchZoteroItems = async () => {
  if (!isZoteroConfigValid.value) return

  try {
    isZoteroLoading.value = true
    const { libraryId, apiKey, tag } = zoteroConfig.value

    // Initialize base URL for group library
    zoteroBaseUrl.value = `https://api.zotero.org/groups/${libraryId}`;
    let isGroupLibrary = true;

    // Test if the `libraryId` belongs to a group or personal library
    let testResponse;
    try {
      // Test request for group library
      testResponse = await axios.get(`${zoteroBaseUrl.value}/items`, {
        params: {
          tag,
          limit: 1,
          includeTrashed: 0
        },
        headers: {
          'Zotero-API-Version': '3',
          'Authorization': `Bearer ${apiKey}`
        }
      });

      // If the response returns an empty list, switch to personal library
      if (Array.isArray(testResponse.data) && testResponse.data.length === 0) {
        // Switch to personal library URL
        zoteroBaseUrl.value = `https://api.zotero.org/users/${libraryId}`;
        isGroupLibrary = false;

        // Test request for personal library
        testResponse = await axios.get(`${zoteroBaseUrl.value}/items`, {
          params: {
            tag,
            limit: 1,
            includeTrashed: 0
          },
          headers: {
            'Zotero-API-Version': '3',
            'Authorization': `Bearer ${apiKey}`
          }
        });

        // If the personal library is also empty, handle appropriately
        if (Array.isArray(testResponse.data) && testResponse.data.length === 0) {
          throw new Error('Both group and personal libraries returned empty lists.');
        }
      }
    } catch (error) {
      // Handle unexpected errors during the test request
      if ((Array.isArray(testResponse.data) && testResponse.data.length === 0) && error.response && error.response.status === 403) {
        ElMessage.error('No item with the given tag found. Please make sure all items are synced to the online library, and check your library ID and API key permissions. (Empty or 403 Forbidden)');
        return;
      }
      ElMessage.error('Failed to connect to Zotero library. Please check your configuration.');
      return;
    }

    // First, get total items count
    const countResponse = await axios.get(
      `${zoteroBaseUrl.value}/items`,
      {
        params: {
          tag,
          limit: 1,
          includeTrashed: 0
        },
        headers: {
          'Zotero-API-Version': '3',
          'Authorization': `Bearer ${apiKey}`
        }
      }
    )

    const totalItems = parseInt(countResponse.headers['total-results'])
    const numberOfRequests = Math.ceil(totalItems / MAX_ITEMS_PER_REQUEST)
    let allFetchedItems = []

    // Fetch all items in parallel
    const requests = Array.from({ length: numberOfRequests }, (_, index) => {
      return axios.get(
        `${zoteroBaseUrl.value}/items`,
        {
          params: {
            tag,
            limit: MAX_ITEMS_PER_REQUEST,
            start: index * MAX_ITEMS_PER_REQUEST,
            includeTrashed: 0,
            itemType: '-attachment || note' // Exclude attachments and notes
          },
          headers: {
            'Zotero-API-Version': '3',
            'Authorization': `Bearer ${apiKey}`
          }
        }
      )
    })

    const responses = await Promise.all(requests)

    // Process all responses
    responses.forEach(response => {
      const items = response.data.map(item => ({
        key: item.key,
        version: item.version,
        title: item.data.title || '',
        abstract: item.data.abstractNote || '',
        creators: item.data.creators || [],
        itemType: item.data.itemType || '',
        date: item.data.date || '',
        publicationTitle: item.data.publicationTitle || '',
        publisher: item.data.publisher || '',
        volume: item.data.volume || '',
        issue: item.data.issue || '',
        pages: item.data.pages || '',
        url: item.data.url || '',
        extra: item.data.extra || '',
      })).filter(item => item.title || item.abstract)

      allFetchedItems = [...allFetchedItems, ...items]
    })

    // Update internal state
    biblioItems.value = allFetchedItems
    removedItems.value = [] // Clear removed items history
    currentSource.value = 'Zotero'

    // Prepare Zotero data to pass to parent
    const zoteroData = {
      config: {
        libraryId: zoteroConfig.value.libraryId,
        apiKey: zoteroConfig.value.apiKey,
        tag: zoteroConfig.value.tag
      },
      baseUrl: zoteroBaseUrl.value
    }

    // Emit the updated items to parent with Zotero configuration
    emit('itemsUpdated', biblioItems.value, removedItems.value, currentSource.value, zoteroData)

    ElMessage.success(`Successfully fetched ${allFetchedItems.length} items from Zotero`)

  } catch (error) {
    console.error('Error fetching Zotero items:', error)
    let errorMessage = 'Failed to fetch items from Zotero'

    if (error.response) {
      switch (error.response.status) {
        case 403:
          errorMessage = 'Invalid API key or insufficient permissions'
          break
        case 404:
          errorMessage = 'Library not found. Please check your Library ID'
          break
        case 429:
          errorMessage = 'Too many requests. Please try again later'
          break
        default:
          errorMessage = error.response.data?.message || errorMessage
      }
    }

    ElMessage.error(errorMessage)
  } finally {
    isZoteroLoading.value = false
  }
}
</script>

<style scoped>
.source-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.truncate-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.zotero-form {
  padding: 30px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 0px;
  margin-bottom: 0px;
}

.article-form {
  width: 100%;
}

.article-input {
  margin-bottom: 30px;
  padding: 20px;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 20px;
}

.title-input,
.abstract-input {
  width: 100%;
}

.center-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.csv-preview {
  margin-top: 20px;
  margin-bottom: 20px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-section {
  margin-bottom: 10px;
}

.more-items {
  margin-top: 8px;
  color: var(--el-text-color-secondary);
  font-style: italic;
}
</style>
